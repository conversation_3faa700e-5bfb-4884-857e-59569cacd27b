# План внедрения схемы сжатия/распаковки мира

## Цель
Внедрить схему сжатия/распаковки созданного мира для оптимизации хранения и передачи данных:
- JSON мир (20-70 МБ) существует в распакованном виде только на фронте в сторе
- В БД и по сети передается только сжатый бинарь
- Алгоритм сжатия: zstd (предпочтительно) или gzip
- PostgreSQL: хранение в колонке BYTEA

## Архитектура решения

### Текущее состояние
- `WorldMap` - основной тип данных мира (shared/src/types/World.ts)
- world-generator-service создает `WorldMap` и отправляет JSON в save-service
- save-service сохраняет в PostgreSQL как JSONB
- Фронт получает и работает с JSON напрямую

### Целевое состояние
- world-generator-service: создает `WorldMap` → сжимает → отправляет бинарь
- save-service: получает бинарь → сохраняет как BYTEA → возвращает бинарь
- Фронт: получает бинарь → распаковывает → работает с JSON → сжимает → отправляет бинарь

## План выполнения

### ✅ Этап 1: Анализ и планирование
- [x] Изучить текущую архитектуру
- [x] Определить точки интеграции
- [x] Создать план выполнения

### ⬜ Этап 2: Добавление утилит сжатия в shared
- [ ] Добавить зависимости для сжатия (zstd или gzip)
- [ ] Создать утилиты сжатия/распаковки в shared/src/utils/compression.ts
- [ ] Добавить типы для работы с сжатыми данными
- [ ] Обновить экспорты в shared/src/index.ts

### ⬜ Этап 3: Модификация save-service
- [ ] Обновить WorldEntity - заменить JSONB колонки на BYTEA
- [ ] Создать миграцию базы данных
- [ ] Обновить WorldsService для работы с бинарными данными
- [ ] Обновить DTO для приема/отдачи бинарных данных
- [ ] Обновить контроллер для работы с новыми типами

### ⬜ Этап 4: Модификация world-generator-service  
- [ ] Обновить AppService - добавить сжатие после генерации мира
- [ ] Обновить отправку данных в save-service (бинарь вместо JSON)
- [ ] Обновить обработку ответов от save-service

### ⬜ Этап 5: Модификация фронта
- [ ] Обновить API клиент для работы с бинарными данными
- [ ] Добавить распаковку при получении мира с сервера
- [ ] Добавить сжатие при отправке мира на сервер
- [ ] Обновить gameStore для прозрачной работы с JSON
- [ ] Обновить все места загрузки/сохранения миров

### ⬜ Этап 6: Тестирование и оптимизация
- [ ] Проверить работу генерации мира
- [ ] Проверить загрузку/сохранение миров
- [ ] Проверить размеры сжатых данных
- [ ] Оптимизировать производительность при необходимости

### ⬜ Этап 7: Финализация
- [ ] Обновить документацию
- [ ] Проверить обратную совместимость
- [ ] Подготовить план миграции существующих данных

## Технические детали

### Библиотеки сжатия
- **Приоритет 1**: `@mongodb-js/zstd` - Node.js биндинги для zstd
- **Приоритет 2**: `pako` - JavaScript реализация gzip/deflate

### Структура данных
```typescript
// Новые типы в shared
export interface CompressedWorldData {
  data: Uint8Array;
  algorithm: 'zstd' | 'gzip';
  originalSize: number;
  compressedSize: number;
}

// Утилиты сжатия
export class CompressionUtils {
  static compress(data: WorldMap): CompressedWorldData
  static decompress(compressed: CompressedWorldData): WorldMap
}
```

### Миграция БД
```sql
-- Новая структура таблицы worlds
ALTER TABLE worlds 
ADD COLUMN world_data BYTEA,
ADD COLUMN compression_algorithm VARCHAR(10),
ADD COLUMN original_size INTEGER,
ADD COLUMN compressed_size INTEGER;

-- Миграция существующих данных будет выполнена отдельно
```

## Ожидаемые результаты
- Уменьшение размера данных в БД на 60-80%
- Уменьшение сетевого трафика на 60-80%
- Сохранение текущего API и функциональности фронта
- Прозрачность изменений для пользователя
