{"name": "@nuclearstory/shared", "version": "1.0.0", "description": "Shared TypeScript models and utilities for NuclearStory", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "watch": "tsc --watch", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["nuclearstory", "shared", "models", "typescript"], "author": "NuclearStory Team", "license": "MIT", "devDependencies": {"sass": "^1.89.2", "typescript": "^5.1.3"}, "dependencies": {"@esbuild/win32-x64": "^0.25.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "esbuild": "^0.25.5", "pako": "^2.1.0"}, "files": ["dist/**/*"]}